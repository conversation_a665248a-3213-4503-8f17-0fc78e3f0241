"""
优化后的 verify_expected_in_response 方法使用示例
"""
from testcases.test_ella.base_ella_test import BaseEllaTest


class ExampleUsage(BaseEllaTest):
    """使用示例类"""
    
    def example_single_text_verification(self):
        """单个文本验证示例"""
        response_text = "蓝牙已成功开启，设备可以被发现"
        expected_text = "蓝牙已成功开启"
        
        # 单个字符串验证
        result = self.verify_expected_in_response(expected_text, response_text)
        print(f"单个文本验证结果: {result}")  # True
    
    def example_multiple_text_verification(self):
        """多个文本验证示例"""
        response_text = "蓝牙已成功开启，设备可以被发现，连接状态良好"
        expected_texts = ["蓝牙已成功开启", "设备可以被发现", "连接状态良好"]
        
        # 多个字符串验证 - 全部匹配
        result = self.verify_expected_in_response(expected_texts, response_text)
        print(f"多个文本验证结果(全部匹配): {result}")  # True
    
    def example_partial_match_verification(self):
        """部分匹配验证示例"""
        response_text = "蓝牙已成功开启，设备可以被发现"
        expected_texts = ["蓝牙已成功开启", "设备可以被发现", "WiFi已连接"]
        
        # 多个字符串验证 - 部分匹配
        result = self.verify_expected_in_response(expected_texts, response_text)
        print(f"多个文本验证结果(部分匹配): {result}")  # False
    
    def example_empty_list_verification(self):
        """空列表验证示例"""
        response_text = "蓝牙已成功开启"
        expected_texts = []
        
        # 空列表验证
        result = self.verify_expected_in_response(expected_texts, response_text)
        print(f"空列表验证结果: {result}")  # True (空列表认为全部匹配)
    
    def example_error_type_verification(self):
        """错误类型验证示例"""
        response_text = "蓝牙已成功开启"
        expected_text = 123  # 错误类型
        
        # 错误类型验证
        result = self.verify_expected_in_response(expected_text, response_text)
        print(f"错误类型验证结果: {result}")  # False


if __name__ == "__main__":
    example = ExampleUsage()
    
    print("=== 优化后的 verify_expected_in_response 方法使用示例 ===\n")
    
    example.example_single_text_verification()
    print()
    
    example.example_multiple_text_verification()
    print()
    
    example.example_partial_match_verification()
    print()
    
    example.example_empty_list_verification()
    print()
    
    example.example_error_type_verification()
