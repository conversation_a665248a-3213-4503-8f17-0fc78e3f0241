"""
Ella状态检查器
负责检查各种系统状态、应用状态和服务状态
"""
import subprocess
import time
from core.logger import log


class EllaStatusChecker:
    """Ella状态检查器"""
    
    def __init__(self, driver=None):
        """
        初始化状态检查器
        
        Args:
            driver: UIAutomator2驱动实例
        """
        self.driver = driver
    
    def check_bluetooth_status(self) -> bool:
        """
        检查蓝牙状态
        
        Returns:
            bool: 蓝牙是否已开启
        """
        try:
            log.info("检查蓝牙状态")
            
            # 通过ADB命令检查蓝牙状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "bluetooth_on"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                bluetooth_status = result.stdout.strip()
                log.info(f"蓝牙状态: (值: {bluetooth_status})")
                is_on = bluetooth_status == "1"
                log.info(f"蓝牙状态: {'开启' if is_on else '关闭'} (值: {bluetooth_status})")
                return is_on
            else:
                log.error(f"获取蓝牙状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查蓝牙状态失败: {e}")
            return False
    
    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态 - 通过ADB命令获取闹钟列表
        
        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("检查闹钟状态")
            
            # 通过ADB命令检查闹钟设置
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                alarm_output = result.stdout
                # 检查是否包含闹钟相关信息
                alarm_indicators = [
                    "com.transsion.deskclock",
                    "AlarmManager",
                    "alarm_clock",
                    "RTC_WAKEUP"
                ]
                
                for indicator in alarm_indicators:
                    if indicator in alarm_output:
                        log.info(f"检测到闹钟相关信息: {indicator}")
                        return True
                
                log.info("未检测到活跃的闹钟")
                return False
            else:
                log.error(f"获取闹钟状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False
    
    def ensure_ella_process(self) -> bool:
        """
        确保当前进程是Ella应用
        
        Returns:
            bool: 当前是否在Ella进程
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查当前进程是否是Ella...")
            
            # 获取当前前台应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')
            
            log.info(f"当前应用: {current_package}")
            log.info(f"当前Activity: {current_activity}")
            
            # 检查是否是Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]
            
            if current_package in ella_packages:
                log.info("✅ 当前在Ella应用进程")
                return True
            else:
                log.warning(f"❌ 当前不在Ella应用进程，当前包名: {current_package}")
                return False
                
        except Exception as e:
            log.error(f"检查Ella进程失败: {e}")
            return False
    
    def check_service_health(self) -> bool:
        """
        检查UIAutomator2服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查UIAutomator2服务健康状态")
            
            # 检查设备信息是否可以正常获取
            device_info = self.driver.device_info
            
            # 检查关键信息是否存在
            if not device_info or not device_info.get('serial'):
                log.warning("设备信息不完整")
                return False
            
            # 尝试获取屏幕信息
            window_size = self.driver.window_size()
            if not window_size or len(window_size) != 2:
                log.warning("无法获取屏幕信息")
                return False
            
            log.info("✅ UIAutomator2服务健康状态良好")
            return True
            
        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查失败: {e}")
            return False
    
    def check_app_started(self, package_name: str) -> bool:
        """
        检查应用是否启动成功
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否启动成功
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            # 方法1: 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            
            if current_package == package_name:
                log.info(f"✅ 应用已在前台: {current_package}")
                return True
            
            # 方法2: 检查应用是否在运行
            running_apps = self.driver.app_list_running()
            if package_name in running_apps:
                log.info(f"✅ 应用正在运行: {package_name}")
                # 尝试切换到前台
                self.driver.app_start(package_name)
                time.sleep(1)
                return True
            
            return False
            
        except Exception as e:
            log.error(f"检查应用启动状态失败: {e}")
            return False
    
    def get_alarm_list(self) -> list:
        """
        获取闹钟列表
        
        Returns:
            list: 闹钟列表
        """
        try:
            log.info("获取闹钟列表")
            alarms = []
            
            # 方法1: 通过dumpsys alarm获取
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode == 0:
                alarm_output = result.stdout
                # 解析闹钟信息
                alarms.extend(self._parse_alarm_database_output(alarm_output))
            
            log.info(f"找到 {len(alarms)} 个闹钟")
            return alarms
            
        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []
    
    def _parse_alarm_database_output(self, output: str) -> list:
        """
        解析闹钟数据库输出
        
        Args:
            output: 数据库输出内容
            
        Returns:
            list: 解析出的闹钟列表
        """
        alarms = []
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                # 查找包含时间信息的行
                if any(keyword in line.lower() for keyword in ['alarm', 'clock', 'time']):
                    # 简单的时间模式匹配
                    import re
                    time_pattern = r'\b([0-1]?[0-9]|2[0-3]):[0-5][0-9]\b'
                    matches = re.findall(time_pattern, line)
                    for match in matches:
                        if match not in [alarm.get('time') for alarm in alarms]:
                            alarms.append({
                                'time': match,
                                'enabled': 'enabled' in line.lower() or 'on' in line.lower(),
                                'source': 'dumpsys'
                            })
        except Exception as e:
            log.debug(f"解析闹钟输出失败: {e}")
        
        return alarms
    
    def clear_all_alarms(self) -> bool:
        """
        清除所有闹钟
        
        Returns:
            bool: 是否成功清除
        """
        try:
            log.info("清除所有闹钟")
            
            # 通过ADB命令清除闹钟
            commands = [
                ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.ALARM_CHANGED"],
                ["adb", "shell", "settings", "put", "system", "alarm_alert", ""],
            ]
            
            success_count = 0
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                except Exception as e:
                    log.debug(f"执行清除命令失败: {e}")
            
            success = success_count > 0
            log.info(f"闹钟清除{'成功' if success else '失败'}")
            return success
            
        except Exception as e:
            log.error(f"清除闹钟失败: {e}")
            return False


if __name__ == '__main__':
    from core.base_driver import driver_manager
    checker = EllaStatusChecker(driver_manager.driver)
    print(checker.check_bluetooth_status())
    # print(checker.get_alarm_list())

